{"host": "https://www.bqgnovels.com", "version": 20250615, "ruleBookInfo": {"mode": "", "request": "", "responseEncode": "utf-8", "method": "GET", "preRequests": [], "engine": "jsonpath", "bookName": "data.title", "bookAuthor": "", "requestEncode": "utf-8", "header": {}, "importUrl": "", "forbidSSL": false, "params": {}, "response": "", "ruleExtra": {"coverUrl": "", "lastUpdateTime": "data.updata_time", "status": "", "bookSize": "", "lastChapterName": "data.update_content", "classify": "", "introduce": ""}, "forbidCookie": false, "chapterListUrl": ""}, "authStatus": 0, "contact": "", "ruleContent": {"mode": "", "method": "GET", "request": "", "responseEncode": "utf-8", "preRequests": [], "commentUrl": "", "engine": "xpath", "next": "", "contents": "//text()", "requestEncode": "utf-8", "playUrl": "", "header": {}, "cleaner": "", "forbidSSL": false, "params": {}, "response": "@js:\nlet jsonData = JSON.parse(html);\n\n// Accessing the first chapter content\nconst chapter = jsonData.data.text[0];\nconst chapterTitle = chapter.tit;\nconst chapterContent = chapter.text;\n\n\n\n\nreturn JSON.stringify(chapterContent)", "forbidCookie": false, "page": ""}, "header": {}, "ruleChapter": {"mode": "", "request": "", "responseEncode": "utf-8", "method": "GET", "preRequests": [], "engine": "jsonpath", "next": "", "chapterList": "[*]", "requestEncode": "utf-8", "bookAuthor": "", "header": {}, "chapterUrl": "url", "forbidSSL": false, "params": {}, "response": "@js:\nlet jsonData = JSON.parse(html);\n\n// Modify jsonData.data.list to add the `url` field to each chapter\njsonData.data.list = jsonData.data.list.map(chapter => ({\n  ...chapter,\n  url: `/api/query/get_book_text?bookId=${jsonData.data.id}&id=${chapter.chapter_id}`\n}));\n\nreturn JSON.stringify(jsonData.data.list)", "chapterName": "tit", "ruleExtra": {"coverUrl": "", "lastUpdateTime": "", "bookSize": "", "lastChapterName": "", "introduce": "", "status": "", "classify": ""}, "forbidCookie": false, "page": ""}, "ruleSearch": {"method": "GET", "requestType": 0, "bookList": "data.list[*]", "bookUrl": "/api/query/get_book_list?bookId={{id}}", "request": "", "forbidSSL": false, "bookAuthor": "author", "url": "/api/query/search?keyword=${keyword}&size=999", "response": "", "mode": "", "engine": "jsonpath", "responseEncode": "utf-8", "header": {}, "params": {}, "pageMax": "", "bookName": "title", "ruleExtra": {"coverUrl": "imgUrl", "bookSize": "", "introduce": "des", "lastChapterName": "update_content", "classify": "", "lastUpdateTime": "update_time", "status": "state\n<js>return value === \"1\" ? \"已完结\" : \"连载\";</js>"}, "forbidCookie": false, "preRequests": [], "requestEncode": "utf-8"}, "type": 1, "ruleFinder": [], "loginUrl": "", "remarks": "", "publicJavascript": "", "author": "晚安", "openParams": [], "cookies": {}, "icon": "https://img.meituan.net/mlive/394dfae238cc31da2606887d6da9a113715769.png", "siteName": "🦌笔趣阁API"}