{"contact": "", "ruleFinder": [{"bookAuthor": "//p[@class=\"info\"]/span[2]/a/text()", "header": {}, "mode": "http", "params": {}, "name": "分类", "bookUrl": "//p[@class=\"info\"]/span[1]/a/@href", "forbidSSL": false, "requestEncode": "utf-8", "ruleExtra": {"coverUrl": "//img/@src", "lastChapterName": "//p[@class=\"last\"]/a/text()", "lastUpdateTime": "", "bookSize": "", "status": "", "introduce": "//p[@class=\"intro\"]/text()", "classify": ""}, "bookList": "//ul[@class=\"librarylist\"]/li", "structure": "http://www.shengxuxu.net/sort/@{_fen}/@{pageIndex}.html", "preRequests": [], "pageMax": "30", "bookName": "//p[@class=\"info\"]/span[1]/a/text()", "method": "GET", "uuid": "1724903959864", "engine": "xpath", "response": "", "responseEncode": "utf-8", "request": "", "forbidCookie": false, "list": [{"type": "fen", "list": "[{\"name\":\"全部小说\",\"value\":\"all\"},{\"name\":\"玄幻小说\",\"value\":\"xuanhuan\"},{\"name\":\"奇幻小说\",\"value\":\"qihuan\"},{\"name\":\"武侠小说\",\"value\":\"wuxia\"},{\"name\":\"仙侠小说\",\"value\":\"xianxia\"},{\"name\":\"都市小说\",\"value\":\"dushi\"},{\"name\":\"历史小说\",\"value\":\"lishi\"},{\"name\":\"军事小说\",\"value\":\"junshi\"},{\"name\":\"游戏小说\",\"value\":\"youxi\"},{\"name\":\"竞技小说\",\"value\":\"jingji\"},{\"name\":\"科幻小说\",\"value\":\"kehuan\"},{\"name\":\"灵异小说\",\"value\":\"lingyi\"},{\"name\":\"同人小说\",\"value\":\"tongren\"},{\"name\":\"女生小说\",\"value\":\"nvsheng\"},{\"name\":\"其他小说\",\"value\":\"qita\"}]", "name": "fen"}]}], "authStatus": 0, "ruleContent": {"header": {}, "mode": "", "params": {}, "cleaner": "", "page": "", "forbidSSL": false, "requestEncode": "utf-8", "contents": "//div[@class='content']/text()", "preRequests": [], "method": "GET", "engine": "xpath", "forbidCookie": false, "response": "", "responseEncode": "utf-8", "request": "", "next": "", "playUrl": "", "commentUrl": ""}, "openParams": [], "version": 2, "header": {}, "ruleSearch": {"method": "GET", "responseEncode": "utf-8", "params": {"searchkey": "{keyword}", "searchtype": "novelname"}, "response": "", "bookUrl": "/div[2]/p/span/a/@href", "mode": "", "url": "http://www.shengxuxu.net/search.html", "requestType": 0, "request": "", "bookAuthor": "/div[2]/p/span[2]<js>\nvalue =value.replace(/作者：/g,'');\nreturn value;\n</js>", "pageMax": "", "forbidSSL": false, "ruleExtra": {"lastChapterName": "/div[2]/p[3]/a", "lastUpdateTime": "", "classify": "", "introduce": "", "status": "", "coverUrl": "//img/@src", "bookSize": ""}, "forbidCookie": false, "preRequests": [], "header": {}, "bookList": "//ul[@class='librarylist']/li", "bookName": "/div[2]/p/span/a", "requestEncode": "utf-8", "engine": "xpath"}, "author": "晚安", "remarks": "", "publicJavascript": "", "icon": "https://img.meituan.net/mlive/394dfae238cc31da2606887d6da9a113715769.png", "host": "http://www.shengxuxu.net/", "cookies": {}, "siteName": "🦌圣墟小说网", "ruleBookInfo": {"importUrl": "", "bookAuthor": "", "header": {}, "mode": "", "params": {}, "forbidSSL": false, "requestEncode": "utf-8", "ruleExtra": {"status": "", "bookSize": "", "lastChapterName": "", "classify": "", "introduce": "", "lastUpdateTime": "", "coverUrl": ""}, "preRequests": [], "chapterListUrl": "", "bookName": "", "engine": "xpath", "method": "GET", "forbidCookie": false, "response": "", "responseEncode": "utf-8", "request": ""}, "ruleChapter": {"bookAuthor": "", "header": {}, "chapterName": "/a", "mode": "", "params": {}, "chapterUrl": "/a/@href", "forbidSSL": false, "requestEncode": "utf-8", "page": "", "ruleExtra": {"bookSize": "", "lastUpdateTime": "", "introduce": "//div[@class='body novelintro ']/text()", "coverUrl": "", "lastChapterName": "", "status": "", "classify": ""}, "preRequests": [], "chapterList": "//div[@class=\"card mt20\"][2]/div[@class=\"body \"]/ul/li", "engine": "xpath", "forbidCookie": false, "method": "GET", "response": "", "responseEncode": "utf-8", "request": "", "next": ""}, "type": 0, "loginUrl": ""}